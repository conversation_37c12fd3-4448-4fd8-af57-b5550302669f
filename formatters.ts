export const formatDate = (dateString?: string): string => {
  if (!dateString) return 'Date not specified';
  
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  } catch {
    return 'Invalid date';
  }
};

export const formatRelativeDate = (dateString?: string): string => {
  if (!dateString) return 'Date not specified';
  
  try {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
    
    if (diffInDays === 0) return 'Today';
    if (diffInDays === 1) return 'Yesterday';
    if (diffInDays < 7) return `${diffInDays} days ago`;
    if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`;
    if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} months ago`;
    return `${Math.floor(diffInDays / 365)} years ago`;
  } catch {
    return 'Invalid date';
  }
};

export const formatTags = (tags?: string): string[] => {
  if (!tags) return [];
  return tags.split(',').map(tag => tag.trim()).filter(Boolean);
};

export const joinTags = (tags: string[]): string => {
  return tags.filter(Boolean).join(', ');
};
