import { Routes, Route } from "react-router-dom";
import { Toaster } from "react-hot-toast";
import { Layout } from "./components/Layout";
import { JobsPage } from "./pages/JobsPage";
import { AddJobPage } from "./pages/AddJobPage";
import { EditJobPage } from "./pages/EditJobPage";

function App() {
  return (
    <>
      <Layout>
        <Routes>
          <Route path="/" element={<JobsPage />} />
          <Route path="/jobs/new" element={<AddJobPage />} />
          <Route path="/jobs/:id/edit" element={<EditJobPage />} />
        </Routes>
      </Layout>
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: "#363636",
            color: "#fff",
          },
          success: {
            duration: 3000,
            style: {
              background: "#10b981",
            },
          },
          error: {
            duration: 5000,
            style: {
              background: "#ef4444",
            },
          },
        }}
      />
    </>
  );
}

export default App;
