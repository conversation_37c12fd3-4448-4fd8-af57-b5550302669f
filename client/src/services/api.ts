import axios from "axios";

const API_BASE_URL = "http://localhost:5000";

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.data) {
      throw error.response.data as any;
    }
    throw { error: "Network error occurred" } as any;
  }
);

export const jobsApi = {
  // Get all jobs with optional filters
  getJobs: async (filters?: any): Promise<any> => {
    const params = new URLSearchParams();

    if (filters?.search) params.append("search", filters.search);
    if (filters?.location) params.append("location", filters.location);
    if (filters?.job_type) params.append("job_type", filters.job_type);
    if (filters?.tag) params.append("tag", filters.tag);
    if (filters?.sort) params.append("sort", filters.sort);
    if (filters?.page) params.append("page", filters.page.toString());
    if (filters?.page_size)
      params.append("page_size", filters.page_size.toString());

    const response = await api.get(`/jobs?${params.toString()}`);
    return response.data;
  },

  // Get a specific job by ID
  getJob: async (id: number): Promise<any> => {
    const response = await api.get(`/jobs/${id}`);
    return response.data;
  },

  // Create a new job
  createJob: async (jobData: any): Promise<any> => {
    const response = await api.post("/jobs/add", jobData);
    return response.data;
  },

  // Update an existing job
  updateJob: async (
    id: number,
    jobData: Partial<any>
  ): Promise<any> => {
    const response = await api.put(`/jobs/${id}`, jobData);
    return response.data;
  },

  // Delete a job
  deleteJob: async (id: number): Promise<void> => {
    await api.delete(`/jobs/${id}`);
  },
};

export default api;
