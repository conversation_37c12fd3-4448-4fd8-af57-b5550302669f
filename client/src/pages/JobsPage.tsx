import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { JobList } from "../components/JobList";
import { FilterBar } from "../components/FilterBar";
import { DeleteJobDialog } from "../components/DeleteJobDialog";
import { type Job, type JobFilters } from "../types/job";

export function JobsPage() {
  const navigate = useNavigate();
  const [filters, setFilters] = useState<JobFilters>({});
  const [refreshKey, setRefreshKey] = useState(0);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [jobToDelete, setJobToDelete] = useState<Job | null>(null);

  const handleAddJob = () => {
    navigate("/jobs/new");
  };

  const handleEditJob = (job: Job) => {
    navigate(`/jobs/${job.id}/edit`);
  };

  const handleDeleteJob = (job: Job) => {
    setJobToDelete(job);
    setShowDeleteDialog(true);
  };

  const handleJobDeleted = (job: Job) => {
    setRefreshKey((prev) => prev + 1);
  };

  const handleFiltersChange = (newFilters: JobFilters) => {
    setFilters(newFilters);
  };

  const handleFiltersReset = () => {
    setRefreshKey((prev) => prev + 1);
  };

  return (
    <>
      <FilterBar
        filters={filters}
        onFiltersChange={handleFiltersChange}
        onReset={handleFiltersReset}
      />

      <JobList
        key={refreshKey}
        filters={filters}
        onAddJob={handleAddJob}
        onEditJob={handleEditJob}
        onDeleteJob={handleDeleteJob}
      />

      <DeleteJobDialog
        job={jobToDelete}
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        onDeleted={handleJobDeleted}
      />
    </>
  );
}
