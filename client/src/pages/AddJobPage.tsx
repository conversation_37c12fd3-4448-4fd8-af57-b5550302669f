import { useNavigate } from "react-router-dom";
import { JobForm } from "../components/JobForm";
import { type Job } from "../types/job";
import { Button } from "../components/ui/button";
import { ArrowLeft } from "lucide-react";

export function AddJobPage() {
  const navigate = useNavigate();

  const handleSave = (job: Job) => {
    navigate("/");
  };

  const handleCancel = () => {
    navigate("/");
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button variant="outline" size="sm" onClick={() => navigate("/")}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Jobs
        </Button>
        <h1 className="text-2xl font-bold text-gray-900">Add New Job</h1>
      </div>

      <JobForm onSave={handleSave} onCancel={handleCancel} />
    </div>
  );
}
