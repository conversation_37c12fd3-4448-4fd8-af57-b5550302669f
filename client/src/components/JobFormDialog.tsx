import { type Job } from "../types/job";
import { JobForm } from "./JobForm";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "./ui/dialog";

interface JobFormDialogProps {
  job?: Job | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSaved?: (job: Job) => void;
}

export function JobFormDialog({
  job,
  open,
  onOpenChange,
  onSaved,
}: JobFormDialogProps) {
  const handleSave = (savedJob: Job) => {
    onSaved?.(savedJob);
    onOpenChange(false);
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{job ? "Edit Job" : "Add New Job"}</DialogTitle>
        </DialogHeader>

        <div className="mt-4">
          <JobForm job={job} onSave={handleSave} onCancel={handleCancel} />
        </div>
      </DialogContent>
    </Dialog>
  );
}
