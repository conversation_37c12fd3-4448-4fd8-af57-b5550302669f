import { useState, useEffect } from "react";
import toast from "react-hot-toast";
import { type Job, type JobFormData } from "../types/job";
import { jobsApi } from "../services/api";
import { JOB_TYPES } from "../utils/constants";
import { joinTags, formatTags } from "../utils/formatters";
import { Button } from "./ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "./ui/card";
import { Input } from "./ui/input";
import { Label } from "./ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select";
import { Loader2, Save, X } from "lucide-react";

interface JobFormProps {
  job?: Job | null;
  onSave?: (job: Job) => void;
  onCancel?: () => void;
}

export function JobForm({ job, onSave, onCancel }: JobFormProps) {
  const [formData, setFormData] = useState<JobFormData>({
    title: "",
    company: "",
    location: "",
    posting_date: "",
    posting_date_raw: "",
    job_type: undefined,
    tags: "",
  });

  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [tagInput, setTagInput] = useState("");

  const isEditing = !!job;

  useEffect(() => {
    if (job) {
      setFormData({
        title: job.title,
        company: job.company,
        location: job.location,
        posting_date: job.posting_date || "",
        posting_date_raw: job.posting_date_raw || "",
        job_type: job.job_type,
        tags: job.tags || "",
      });
      setTagInput(formatTags(job.tags).join(", "));
    }
  }, [job]);

  const handleInputChange = (field: keyof JobFormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  const handleTagInputChange = (value: string) => {
    setTagInput(value);
    const tags = value
      .split(",")
      .map((tag) => tag.trim())
      .filter(Boolean);
    handleInputChange("tags", joinTags(tags));
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = "Title is required";
    }
    if (!formData.company.trim()) {
      newErrors.company = "Company is required";
    }
    if (!formData.location.trim()) {
      newErrors.location = "Location is required";
    }

    if (formData.posting_date) {
      try {
        new Date(formData.posting_date);
      } catch {
        newErrors.posting_date = "Invalid date format";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setLoading(true);
    try {
      let savedJob: Job;

      if (isEditing && job) {
        savedJob = await jobsApi.updateJob(job.id, formData);
        toast.success("Job updated successfully!");
      } else {
        savedJob = await jobsApi.createJob(formData);
        toast.success("Job created successfully!");
      }

      onSave?.(savedJob);
    } catch (error: any) {
      if (error.fields) {
        setErrors(error.fields);
        toast.error("Please fix the form errors");
      } else {
        const errorMessage = error.error || "An error occurred";
        setErrors({ general: errorMessage });
        toast.error(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="max-w-2xl mx-auto shadow-lg border-0 bg-white/95 backdrop-blur-sm">
      <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
        <CardTitle className="flex items-center justify-between text-gray-900">
          <span className="text-xl">
            {isEditing ? "Edit Job" : "Add New Job"}
          </span>
          {onCancel && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onCancel}
              className="hover:bg-white/50"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </CardTitle>
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {errors.general && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-red-600 text-sm">{errors.general}</p>
            </div>
          )}

          {/* Title */}
          <div className="space-y-2">
            <Label htmlFor="title">Job Title *</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => handleInputChange("title", e.target.value)}
              placeholder="e.g. Senior Frontend Developer"
              className={errors.title ? "border-red-500" : ""}
            />
            {errors.title && (
              <p className="text-red-600 text-sm">{errors.title}</p>
            )}
          </div>

          {/* Company */}
          <div className="space-y-2">
            <Label htmlFor="company">Company *</Label>
            <Input
              id="company"
              value={formData.company}
              onChange={(e) => handleInputChange("company", e.target.value)}
              placeholder="e.g. Tech Corp Inc."
              className={errors.company ? "border-red-500" : ""}
            />
            {errors.company && (
              <p className="text-red-600 text-sm">{errors.company}</p>
            )}
          </div>

          {/* Location */}
          <div className="space-y-2">
            <Label htmlFor="location">Location *</Label>
            <Input
              id="location"
              value={formData.location}
              onChange={(e) => handleInputChange("location", e.target.value)}
              placeholder="e.g. San Francisco, CA"
              className={errors.location ? "border-red-500" : ""}
            />
            {errors.location && (
              <p className="text-red-600 text-sm">{errors.location}</p>
            )}
          </div>

          {/* Job Type */}
          <div className="space-y-2">
            <Label htmlFor="job_type">Job Type</Label>
            <Select
              value={formData.job_type || ""}
              onValueChange={(value) => handleInputChange("job_type", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select job type" />
              </SelectTrigger>
              <SelectContent>
                {JOB_TYPES.map((type) => (
                  <SelectItem key={type} value={type}>
                    {type}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Posting Date */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="posting_date">Posting Date</Label>
              <Input
                id="posting_date"
                type="date"
                value={formData.posting_date}
                onChange={(e) =>
                  handleInputChange("posting_date", e.target.value)
                }
                className={errors.posting_date ? "border-red-500" : ""}
              />
              {errors.posting_date && (
                <p className="text-red-600 text-sm">{errors.posting_date}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="posting_date_raw">Posting Date (Raw)</Label>
              <Input
                id="posting_date_raw"
                value={formData.posting_date_raw}
                onChange={(e) =>
                  handleInputChange("posting_date_raw", e.target.value)
                }
                placeholder="e.g. 2 days ago"
              />
            </div>
          </div>

          {/* Tags */}
          <div className="space-y-2">
            <Label htmlFor="tags">Tags</Label>
            <Input
              id="tags"
              value={tagInput}
              onChange={(e) => handleTagInputChange(e.target.value)}
              placeholder="e.g. React, TypeScript, Remote (comma-separated)"
            />
            <p className="text-gray-500 text-sm">
              Separate multiple tags with commas
            </p>
          </div>

          {/* Submit Buttons */}
          <div className="flex gap-3 pt-4">
            <Button type="submit" disabled={loading} className="flex-1">
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  {isEditing ? "Updating..." : "Creating..."}
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  {isEditing ? "Update Job" : "Create Job"}
                </>
              )}
            </Button>

            {onCancel && (
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
