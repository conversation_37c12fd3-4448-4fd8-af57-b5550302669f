import { useState, useEffect } from "react";
import { type Job, type JobFilters } from "../types/job";
import { jobsApi } from "../services/api";
import { JobCard } from "./JobCard";
import { JobCardSkeleton } from "./JobCardSkeleton";
import { <PERSON><PERSON> } from "./ui/button";
import { Card, CardContent } from "./ui/card";
import { Loader2, Plus, AlertCircle, Briefcase } from "lucide-react";
import { DEFAULT_PAGE_SIZE } from "../utils/constants";

interface JobListProps {
  filters?: JobFilters;
  onAddJob?: () => void;
  onEditJob?: (job: Job) => void;
  onDeleteJob?: (job: Job) => void;
}

export function JobList({
  filters,
  onAddJob,
  onEditJob,
  onDeleteJob,
}: JobListProps) {
  const [jobs, setJobs] = useState<Job[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);

  const fetchJobs = async (page = 1, reset = false) => {
    try {
      setLoading(true);
      setError(null);

      const response = await jobsApi.getJobs({
        ...filters,
        page,
        page_size: DEFAULT_PAGE_SIZE,
      });

      if (reset) {
        setJobs(response.jobs);
      } else {
        setJobs((prev) => [...prev, ...response.jobs]);
      }

      setTotal(response.total);
      setCurrentPage(page);
      setHasMore(
        response.jobs.length === DEFAULT_PAGE_SIZE &&
          page * DEFAULT_PAGE_SIZE < response.total
      );
    } catch (err: any) {
      setError(err.error || "Failed to fetch jobs");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchJobs(1, true);
  }, [filters]);

  const handleLoadMore = () => {
    if (!loading && hasMore) {
      fetchJobs(currentPage + 1, false);
    }
  };

  const handleRefresh = () => {
    fetchJobs(1, true);
  };

  if (loading && jobs.length === 0) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <div className="h-8 bg-gray-200 rounded w-48 mb-2 animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded w-32 animate-pulse"></div>
          </div>
          {onAddJob && (
            <Button onClick={onAddJob}>
              <Plus className="h-4 w-4 mr-2" />
              Add Job
            </Button>
          )}
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {Array.from({ length: 6 }).map((_, index) => (
            <JobCardSkeleton key={index} />
          ))}
        </div>
      </div>
    );
  }

  if (error && jobs.length === 0) {
    return (
      <Card className="mx-auto max-w-md">
        <CardContent className="pt-6">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Error Loading Jobs
            </h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button onClick={handleRefresh} variant="outline">
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (jobs.length === 0) {
    return (
      <Card className="mx-auto max-w-md">
        <CardContent className="pt-6">
          <div className="text-center">
            <Briefcase className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              No Jobs Found
            </h3>
            <p className="text-gray-600 mb-4">
              {filters?.search || filters?.location || filters?.job_type
                ? "No jobs match your current filters."
                : "No jobs have been posted yet."}
            </p>
            {onAddJob && (
              <Button onClick={onAddJob}>
                <Plus className="h-4 w-4 mr-2" />
                Add First Job
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Job Listings</h2>
          <p className="text-gray-600">
            {total} {total === 1 ? "job" : "jobs"} found
          </p>
        </div>
        {onAddJob && (
          <Button onClick={onAddJob}>
            <Plus className="h-4 w-4 mr-2" />
            Add Job
          </Button>
        )}
      </div>

      {/* Jobs Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {jobs.map((job) => (
          <JobCard
            key={job.id}
            job={job}
            onEdit={onEditJob}
            onDelete={onDeleteJob}
          />
        ))}
      </div>

      {/* Load More */}
      {hasMore && (
        <div className="text-center">
          <Button
            onClick={handleLoadMore}
            disabled={loading}
            variant="outline"
            size="lg"
          >
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Loading...
              </>
            ) : (
              "Load More Jobs"
            )}
          </Button>
        </div>
      )}

      {/* Error for load more */}
      {error && jobs.length > 0 && (
        <div className="text-center">
          <p className="text-red-600 mb-2">{error}</p>
          <Button onClick={handleRefresh} variant="outline" size="sm">
            Retry
          </Button>
        </div>
      )}
    </div>
  );
}
