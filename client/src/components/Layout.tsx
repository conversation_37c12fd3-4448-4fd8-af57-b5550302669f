import { ReactNode } from "react";
import { Link, useLocation } from "react-router-dom";
import { Briefcase, Plus, Home } from "lucide-react";
import { Button } from "./ui/button";

interface LayoutProps {
  children: ReactNode;
}

export function Layout({ children }: LayoutProps) {
  const location = useLocation();
  const isHomePage = location.pathname === "/";

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link
              to="/"
              className="flex items-center hover:opacity-80 transition-opacity"
            >
              <Briefcase className="h-8 w-8 text-blue-600 mr-3" />
              <div>
                <h1 className="text-xl font-bold text-gray-900">
                  BitBash Job Board
                </h1>
                <p className="text-xs text-gray-500 hidden sm:block">
                  Find your next opportunity
                </p>
              </div>
            </Link>

            <nav className="flex items-center gap-4">
              {!isHomePage && (
                <Button variant="outline" size="sm" asChild>
                  <Link to="/">
                    <Home className="h-4 w-4 mr-2" />
                    <span className="hidden sm:inline">Home</span>
                  </Link>
                </Button>
              )}

              <Button size="sm" asChild>
                <Link to="/jobs/new">
                  <Plus className="h-4 w-4 mr-2" />
                  <span className="hidden sm:inline">Add Job</span>
                  <span className="sm:hidden">Add</span>
                </Link>
              </Button>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
        <div className="min-h-[calc(100vh-200px)]">{children}</div>
      </main>

      {/* Footer */}
      <footer className="bg-white border-t mt-auto">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
            <p className="text-gray-500 text-sm text-center sm:text-left">
              © 2024 BitBash Job Board. Built with React & Flask.
            </p>
            <div className="flex items-center gap-4 text-sm text-gray-500">
              <span>Made with ❤️ for developers</span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
