import { Link, useLocation } from "react-router-dom";
import { Briefcase, Plus, Home } from "lucide-react";
import { Button } from "./ui/button";
import type { ReactNode } from "react";

interface LayoutProps {
  children: ReactNode;
}

export function Layout({ children }: LayoutProps) {
  const location = useLocation();
  const isHomePage = location.pathname === "/";

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-blue-50 via-blue-100 to-indigo-100">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-md shadow-lg border-b sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-20">
            <Link
              to="/"
              className="flex items-center hover:opacity-90 transition-opacity"
            >
              <Briefcase className="h-10 w-10 text-indigo-600 mr-4 drop-shadow-md" />
              <div>
                <h1 className="text-2xl font-extrabold text-indigo-900 tracking-tight">
                  BitBash Job Board
                </h1>
                <p className="text-xs text-indigo-400 hidden sm:block font-medium">
                  Find your next opportunity
                </p>
              </div>
            </Link>

            <nav className="flex items-center gap-3">
              {!isHomePage && (
                <Button
                  variant="outline"
                  size="sm"
                  className="border-indigo-300 text-indigo-700 hover:bg-indigo-50"
                  asChild
                >
                  <Link to="/">
                    <Home className="h-5 w-5 mr-2" />
                    <span className="hidden sm:inline">Home</span>
                  </Link>
                </Button>
              )}

              <Button
                size="sm"
                className="bg-gradient-to-r from-indigo-500 to-blue-500 text-white font-semibold shadow-md hover:from-indigo-600 hover:to-blue-600"
                asChild
              >
                <Link to="/jobs/new">
                  <Plus className="h-5 w-5 mr-2" />
                  <span className="hidden sm:inline">Add Job</span>
                  <span className="sm:hidden">Add</span>
                </Link>
              </Button>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      {/* <main className="max-w-7xl mx-auto px-2 sm:px-6 lg:px-8 py-8 sm:py-12">
        <div className="min-h-[calc(100vh-220px)] bg-white/80 rounded-xl shadow-lg p-4 sm:p-8 backdrop-blur-md">
          {children}
        </div>
      </main> */}

      {/* Footer */}
      {/* <footer className="bg-white/90 border-t mt-auto shadow-inner">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
            <p className="text-indigo-500 text-sm text-center sm:text-left font-medium">
              © 2024 BitBash Job Board. Built with{" "}
              <span className="font-bold">React</span> &{" "}
              <span className="font-bold">Flask</span>.
            </p>
            <div className="flex items-center gap-4 text-sm text-indigo-400">
              <span>
                Made with <span className="text-pink-500">❤️</span> for
                developers
              </span>
            </div>
          </div>
        </div>
      </footer> */}
    </div>
  );
}
