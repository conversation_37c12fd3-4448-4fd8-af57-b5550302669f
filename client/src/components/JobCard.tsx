import type { Job } from "../types/job";
import {
  formatDate,
  formatRelativeDate,
  formatTags,
} from "../utils/formatters";
import { But<PERSON> } from "./ui/button";
import { Card, CardContent, CardFooter, CardHeader } from "./ui/card";
import { Badge } from "./ui/badge";
import { MapPin, Building2, Calendar, Clock, Edit, Trash2 } from "lucide-react";

interface JobCardProps {
  job: Job;
  onEdit?: (job: Job) => void;
  onDelete?: (job: Job) => void;
  showActions?: boolean;
}

export function JobCard({
  job,
  onEdit,
  onDelete,
  showActions = true,
}: JobCardProps) {
  const tags = formatTags(job.tags);

  return (
    <Card className="h-full hover:shadow-xl hover:scale-[1.02] transition-all duration-300 border-l-4 border-l-blue-500 bg-white/80 backdrop-blur-sm">
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start gap-4">
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-semibold text-gray-900 truncate hover:text-blue-600 transition-colors">
              {job.title}
            </h3>
            <div className="flex items-center gap-1 text-gray-600 mt-1">
              <Building2 className="h-4 w-4 flex-shrink-0" />
              <span className="truncate">{job.company}</span>
            </div>
          </div>
          {job.job_type && (
            <Badge
              variant="secondary"
              className="flex-shrink-0 bg-blue-100 text-blue-800 hover:bg-blue-200 transition-colors"
            >
              {job.job_type}
            </Badge>
          )}
        </div>
      </CardHeader>

      <CardContent className="pb-3">
        <div className="space-y-2">
          <div className="flex items-center gap-1 text-gray-600">
            <MapPin className="h-4 w-4 flex-shrink-0" />
            <span className="truncate">{job.location}</span>
          </div>

          {(job.posting_date || job.posting_date_raw) && (
            <div className="flex items-center gap-1 text-gray-500 text-sm">
              <Calendar className="h-4 w-4 flex-shrink-0" />
              <span>
                {job.posting_date_raw || formatDate(job.posting_date)}
              </span>
              {job.posting_date && (
                <>
                  <Clock className="h-3 w-3 ml-2 flex-shrink-0" />
                  <span className="text-xs">
                    {formatRelativeDate(job.posting_date)}
                  </span>
                </>
              )}
            </div>
          )}

          {tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mt-3">
              {tags.slice(0, 3).map((tag, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
              {tags.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{tags.length - 3} more
                </Badge>
              )}
            </div>
          )}
        </div>
      </CardContent>

      {showActions && (
        <CardFooter className="pt-3 border-t bg-gray-50/50">
          <div className="flex gap-2 w-full">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onEdit?.(job)}
              className="flex-1 hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700 transition-all"
            >
              <Edit className="h-4 w-4 mr-1" />
              Edit
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onDelete?.(job)}
              className="flex-1 text-red-600 hover:text-red-700 hover:bg-red-50 hover:border-red-300 transition-all"
            >
              <Trash2 className="h-4 w-4 mr-1" />
              Delete
            </Button>
          </div>
        </CardFooter>
      )}
    </Card>
  );
}
