import { useState, useEffect } from "react";
import { type JobFilters } from "../types/job";
import { JOB_TYPES, SORT_OPTIONS } from "../utils/constants";
import { Button } from "./ui/button";
import { Input } from "./ui/input";
import { Label } from "./ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select";
import { Card, CardContent } from "./ui/card";
import { Search, Filter, X, RotateCcw } from "lucide-react";

interface FilterBarProps {
  filters: JobFilters;
  onFiltersChange: (filters: JobFilters) => void;
  onReset?: () => void;
}

export function FilterBar({
  filters,
  onFiltersChange,
  onReset,
}: FilterBarProps) {
  const [localFilters, setLocalFilters] = useState<JobFilters>(filters);
  const [showAdvanced, setShowAdvanced] = useState(false);

  useEffect(() => {
    setLocalFilters(filters);
  }, [filters]);

  const handleFilterChange = (
    key: keyof JobFilters,
    value: string | undefined
  ) => {
    const newFilters = { ...localFilters, [key]: value || undefined };
    setLocalFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const handleReset = () => {
    const resetFilters: JobFilters = {};
    setLocalFilters(resetFilters);
    onFiltersChange(resetFilters);
    setShowAdvanced(false);
    onReset?.();
  };

  const hasActiveFilters = Object.values(localFilters).some(
    (value) => value !== undefined && value !== ""
  );

  return (
    <Card className="mb-6">
      <CardContent className="pt-6">
        <div className="space-y-4">
          {/* Main Search Row */}
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search Input */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search jobs by title or company..."
                  value={localFilters.search || ""}
                  onChange={(e) => handleFilterChange("search", e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Quick Job Type Filter */}
            <div className="w-full sm:w-48">
              <Select
                value={localFilters.job_type || ""}
                onValueChange={(value) => handleFilterChange("job_type", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Job Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Types</SelectItem>
                  {JOB_TYPES.map((type) => (
                    <SelectItem key={type} value={type}>
                      {type}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Sort */}
            <div className="w-full sm:w-48">
              <Select
                value={localFilters.sort || "posting_date_desc"}
                onValueChange={(value) => handleFilterChange("sort", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  {SORT_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Advanced Toggle & Reset */}
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowAdvanced(!showAdvanced)}
                className="flex items-center gap-2"
              >
                <Filter className="h-4 w-4" />
                {showAdvanced ? "Less" : "More"}
              </Button>

              {hasActiveFilters && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleReset}
                  className="flex items-center gap-2"
                >
                  <RotateCcw className="h-4 w-4" />
                  Reset
                </Button>
              )}
            </div>
          </div>

          {/* Advanced Filters */}
          {showAdvanced && (
            <div className="border-t pt-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                {/* Location Filter */}
                <div className="space-y-2">
                  <Label htmlFor="location-filter">Location</Label>
                  <Input
                    id="location-filter"
                    placeholder="Filter by location..."
                    value={localFilters.location || ""}
                    onChange={(e) =>
                      handleFilterChange("location", e.target.value)
                    }
                  />
                </div>

                {/* Tag Filter */}
                <div className="space-y-2">
                  <Label htmlFor="tag-filter">Tag</Label>
                  <Input
                    id="tag-filter"
                    placeholder="Filter by tag..."
                    value={localFilters.tag || ""}
                    onChange={(e) => handleFilterChange("tag", e.target.value)}
                  />
                </div>

                {/* Page Size */}
                <div className="space-y-2">
                  <Label htmlFor="page-size">Results per page</Label>
                  <Select
                    value={localFilters.page_size?.toString() || "10"}
                    onValueChange={(value) =>
                      handleFilterChange("page_size", parseInt(value))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="5">5</SelectItem>
                      <SelectItem value="10">10</SelectItem>
                      <SelectItem value="20">20</SelectItem>
                      <SelectItem value="50">50</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          )}

          {/* Active Filters Display */}
          {hasActiveFilters && (
            <div className="flex flex-wrap gap-2 pt-2 border-t">
              <span className="text-sm text-gray-600 font-medium">
                Active filters:
              </span>

              {localFilters.search && (
                <div className="flex items-center gap-1 bg-blue-100 text-blue-800 px-2 py-1 rounded-md text-sm">
                  <span>Search: "{localFilters.search}"</span>
                  <button
                    onClick={() => handleFilterChange("search", "")}
                    className="hover:bg-blue-200 rounded p-0.5"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </div>
              )}

              {localFilters.job_type && (
                <div className="flex items-center gap-1 bg-green-100 text-green-800 px-2 py-1 rounded-md text-sm">
                  <span>Type: {localFilters.job_type}</span>
                  <button
                    onClick={() => handleFilterChange("job_type", "")}
                    className="hover:bg-green-200 rounded p-0.5"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </div>
              )}

              {localFilters.location && (
                <div className="flex items-center gap-1 bg-purple-100 text-purple-800 px-2 py-1 rounded-md text-sm">
                  <span>Location: "{localFilters.location}"</span>
                  <button
                    onClick={() => handleFilterChange("location", "")}
                    className="hover:bg-purple-200 rounded p-0.5"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </div>
              )}

              {localFilters.tag && (
                <div className="flex items-center gap-1 bg-orange-100 text-orange-800 px-2 py-1 rounded-md text-sm">
                  <span>Tag: "{localFilters.tag}"</span>
                  <button
                    onClick={() => handleFilterChange("tag", "")}
                    className="hover:bg-orange-200 rounded p-0.5"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
