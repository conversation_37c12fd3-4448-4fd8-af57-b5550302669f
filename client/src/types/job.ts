export interface Job {
  id: number;
  title: string;
  company: string;
  location: string;
  posting_date?: string;
  posting_date_raw?: string;
  job_type?: "Full-time" | "Part-time" | "Contract" | "Internship";
  tags?: string;
}

export interface JobFormData {
  title: string;
  company: string;
  location: string;
  posting_date?: string;
  posting_date_raw?: string;
  job_type?: "Full-time" | "Part-time" | "Contract" | "Internship";
  tags?: string;
}

export interface JobsResponse {
  jobs: Job[];
  total: number;
  page: number;
  page_size: number;
}

export interface JobFilters {
  search?: string;
  location?: string;
  job_type?: string;
  tag?: string;
  sort?: string;
  page?: number;
  page_size?: number;
}

export interface ApiError {
  error: string;
  fields?: Record<string, string>;
}
