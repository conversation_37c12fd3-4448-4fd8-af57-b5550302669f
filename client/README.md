# BitBash Job Board - Frontend

A modern, responsive React frontend for the BitBash Job Board application.

## 🚀 Features

- **Modern UI**: Built with React 19, TypeScript, and Tailwind CSS
- **Component Library**: Uses shadcn/ui for consistent, accessible components
- **Responsive Design**: Mobile-first design that works on all devices
- **Real-time Updates**: Hot module replacement for fast development
- **Form Validation**: Client-side validation with error handling
- **Toast Notifications**: User-friendly feedback for all actions
- **Loading States**: Skeleton loaders and loading indicators
- **Search & Filter**: Advanced filtering and search capabilities
- **Routing**: Client-side routing with React Router

## 🛠️ Tech Stack

- **React 19** - UI library
- **TypeScript** - Type safety
- **Vite** - Build tool and dev server
- **Tailwind CSS** - Utility-first CSS framework
- **shadcn/ui** - Component library
- **React Router** - Client-side routing
- **Axios** - HTTP client
- **React Hot Toast** - Toast notifications
- **Lucide React** - Icon library

## 📦 Installation

```bash
npm install
```

## 🏃‍♂️ Development

```bash
npm run dev
```

The application will be available at `http://localhost:5173`

## 🏗️ Build

```bash
npm run build
```

## 📁 Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── ui/             # shadcn/ui components
│   ├── JobCard.tsx     # Job listing card
│   ├── JobList.tsx     # Job listings container
│   ├── JobForm.tsx     # Add/edit job form
│   ├── FilterBar.tsx   # Search and filter controls
│   └── Layout.tsx      # Main layout wrapper
├── pages/              # Page components
│   ├── JobsPage.tsx    # Main jobs listing page
│   ├── AddJobPage.tsx  # Add new job page
│   └── EditJobPage.tsx # Edit job page
├── services/           # API services
│   └── api.ts          # API client and endpoints
├── types/              # TypeScript type definitions
│   └── job.ts          # Job-related types
├── utils/              # Utility functions
│   ├── constants.ts    # App constants
│   └── formatters.ts   # Data formatting utilities
└── App.tsx             # Main app component
```

## 🎨 UI Components

The application uses a consistent design system with:

- **Cards**: For job listings and forms
- **Buttons**: Primary, secondary, and outline variants
- **Forms**: With validation and error states
- **Modals**: For confirmations and forms
- **Toast**: For user feedback
- **Loading**: Skeleton loaders and spinners

## 🔗 API Integration

The frontend connects to the Flask backend API at `http://localhost:5000` with endpoints for:

- `GET /jobs` - List jobs with filtering
- `POST /jobs/add` - Create new job
- `GET /jobs/:id` - Get specific job
- `PUT /jobs/:id` - Update job
- `DELETE /jobs/:id` - Delete job

## 📱 Responsive Design

The application is fully responsive with:

- Mobile-first design approach
- Responsive grid layouts
- Adaptive navigation
- Touch-friendly interactions
- Optimized for all screen sizes

## 🎯 Key Features

### Job Management
- Create, read, update, and delete jobs
- Form validation with real-time feedback
- Toast notifications for user actions

### Search & Filter
- Search by job title or company
- Filter by job type, location, and tags
- Sort by various criteria
- Advanced filtering options

### User Experience
- Loading skeletons during data fetch
- Responsive design for all devices
- Smooth animations and transitions
- Accessible UI components

## 🚀 Getting Started

1. Make sure the backend is running on `http://localhost:5000`
2. Install dependencies: `npm install`
3. Start the development server: `npm run dev`
4. Open `http://localhost:5173` in your browser

The application will automatically connect to the backend API and you can start managing jobs immediately!
